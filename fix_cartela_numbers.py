#!/usr/bin/env python3
"""
Fix cartela numbers to ensure they are sequential and unique from 1 to 20,000
"""

import json

def fix_cartela_numbers():
    """Fix cartela numbers to be sequential 1-20000"""
    print("🔧 Fixing cartela numbers...")
    
    # Load current data
    try:
        with open("arrays.json", 'r') as f:
            data = json.load(f)
        
        print(f"✅ Loaded {len(data)} cards")
        
        # Sort by ID to maintain order
        data.sort(key=lambda x: x['id'])
        
        # Fix cartela numbers to be sequential
        for i, card in enumerate(data):
            card['cartela_no'] = i + 1  # Sequential from 1 to 20000
        
        # Verify uniqueness
        ids = [card['id'] for card in data]
        cartelas = [card['cartela_no'] for card in data]
        
        print(f"📊 Verification:")
        print(f"   Total cards: {len(data)}")
        print(f"   Unique IDs: {len(set(ids))}")
        print(f"   Unique cartela_nos: {len(set(cartelas))}")
        print(f"   ID range: {min(ids)} to {max(ids)}")
        print(f"   Cartela range: {min(cartelas)} to {max(cartelas)}")
        
        # Check for duplicates
        id_duplicates = len(ids) - len(set(ids))
        cartela_duplicates = len(cartelas) - len(set(cartelas))
        
        if id_duplicates == 0 and cartela_duplicates == 0:
            print("✅ All IDs and cartela numbers are now unique!")
        else:
            print(f"❌ Still have duplicates: {id_duplicates} ID duplicates, {cartela_duplicates} cartela duplicates")
            return False
        
        # Save fixed data
        with open("arrays.json", 'w') as f:
            json.dump(data, f, indent=2)
        
        print(f"✅ Successfully saved fixed data to arrays.json")
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def verify_all_uniqueness():
    """Verify that IDs, cartela_nos, and bingo combinations are all unique"""
    print("\n🔍 Final verification of all uniqueness...")
    
    try:
        with open("arrays.json", 'r') as f:
            data = json.load(f)
        
        # Check IDs
        ids = [card['id'] for card in data]
        unique_ids = len(set(ids))
        
        # Check cartela numbers
        cartelas = [card['cartela_no'] for card in data]
        unique_cartelas = len(set(cartelas))
        
        # Check bingo number combinations
        signatures = set()
        for card in data:
            signature = ','.join(map(str, sorted(card['bingo_numbers'])))
            signatures.add(signature)
        unique_combinations = len(signatures)
        
        total_cards = len(data)
        
        print(f"📊 Final Results:")
        print(f"   Total cards: {total_cards}")
        print(f"   Unique IDs: {unique_ids}")
        print(f"   Unique cartela_nos: {unique_cartelas}")
        print(f"   Unique bingo combinations: {unique_combinations}")
        
        if unique_ids == unique_cartelas == unique_combinations == total_cards:
            print("🎉 PERFECT! All IDs, cartela numbers, and bingo combinations are unique!")
            return True
        else:
            print("❌ Still have uniqueness issues")
            return False
            
    except Exception as e:
        print(f"❌ Error during verification: {e}")
        return False

def main():
    """Main function"""
    print("🎯 FIXING CARTELA NUMBER DUPLICATES")
    print("=" * 50)
    
    if fix_cartela_numbers():
        verify_all_uniqueness()
    else:
        print("❌ Failed to fix cartela numbers")

if __name__ == "__main__":
    main()
