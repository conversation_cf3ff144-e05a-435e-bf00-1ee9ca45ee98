#!/usr/bin/env python3
"""
Remove quotes from F in bingo_numbers arrays
Change "F" to F (without quotes)
"""

import json

def fix_f_quotes():
    """Remove quotes from F in all bingo_numbers arrays"""
    print("🔄 Removing quotes from F in all bingo cards...")
    
    try:
        # Load current data
        with open("arrays.json", 'r') as f:
            data = json.load(f)
        
        print(f"✅ Loaded {len(data)} cards")
        
        # Process each card
        modified_count = 0
        for card in data:
            bingo_numbers = card['bingo_numbers']
            
            # Find and replace "F" with F (without quotes)
            for i, item in enumerate(bingo_numbers):
                if item == "F":
                    bingo_numbers[i] = "F"  # This will be saved as F without quotes in JSON
                    modified_count += 1
                    break
        
        print(f"✅ Modified {modified_count} cards")
        
        # Save the modified data
        with open("arrays.json", 'w') as f:
            # Use custom JSON encoding to write F without quotes
            json_str = json.dumps(data, indent=2)
            # Replace "F" with F (without quotes)
            json_str = json_str.replace('"F"', 'F')
            f.write(json_str)
        
        print(f"✅ Successfully saved modified data to arrays.json")
        print(f"📊 All F entries now appear without quotes")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Main function"""
    print("🎯 REMOVING QUOTES FROM F")
    print("=" * 50)
    
    fix_f_quotes()

if __name__ == "__main__":
    main()
