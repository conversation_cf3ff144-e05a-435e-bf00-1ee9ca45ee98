#!/usr/bin/env python3
"""
Convert existing bingo arrays to new format
Converts from grid format to flat array format as requested
"""

import json
import os
from typing import List, Dict, Any, Optional

def load_existing_data(filename: str = "arrays_backup.json") -> Optional[Dict[str, Any]]:
    """Load existing bingo data from JSON file"""
    try:
        if not os.path.exists(filename):
            print(f"❌ File not found: {filename}")
            return None
            
        with open(filename, 'r') as f:
            data = json.load(f)
            
        print(f"✅ Successfully loaded data from {filename}")
        print(f"📊 Total boards found: {data.get('total_boards', 0)}")
        return data
        
    except Exception as e:
        print(f"❌ Error loading data: {e}")
        return None

def convert_grid_to_flat_array(grid: List[List[int]]) -> List[int]:
    """
    Convert 5x5 grid to flat array of 24 numbers (excluding center free space)
    Grid layout:
    [0,0] [0,1] [0,2] [0,3] [0,4]
    [1,0] [1,1] [1,2] [1,3] [1,4]
    [2,0] [2,1] [2,2] [2,3] [2,4]  <- [2,2] is center (FREE)
    [3,0] [3,1] [3,2] [3,3] [3,4]
    [4,0] [4,1] [4,2] [4,3] [4,4]
    """
    flat_array = []
    
    for row_idx, row in enumerate(grid):
        for col_idx, cell in enumerate(row):
            # Skip center cell (row 2, col 2) which is the FREE space
            if row_idx == 2 and col_idx == 2:
                continue
            
            # Only include valid numbers (not None and not 0)
            if cell is not None and cell != 0:
                flat_array.append(cell)
            else:
                # If we have invalid data, we might want to handle this
                print(f"⚠️  Warning: Invalid cell value {cell} at position [{row_idx},{col_idx}]")
    
    return flat_array

def convert_to_new_format(data: Dict[str, Any], start_id: int = 7001) -> List[Dict[str, Any]]:
    """Convert data to new format"""
    new_format = []
    
    for board_data in data.get('boards', []):
        board_number = board_data.get('board_number', 0)
        grid = board_data.get('grid', [])
        
        # Convert grid to flat array
        bingo_numbers = convert_grid_to_flat_array(grid)
        
        # Only include boards with complete data (24 numbers)
        if len(bingo_numbers) == 24:
            new_entry = {
                "id": start_id + board_number - 1,  # Start from 7001, board 1 -> id 7001
                "cartela_no": board_number,
                "bingo_numbers": bingo_numbers
            }
            new_format.append(new_entry)
        else:
            print(f"⚠️  Skipping board {board_number}: only {len(bingo_numbers)}/24 valid numbers")
    
    return new_format

def save_new_format(data: List[Dict[str, Any]], filename: str = "arrays.json") -> bool:
    """Save data in new format to JSON file"""
    try:
        with open(filename, 'w') as f:
            json.dump(data, f, indent=2)
        
        print(f"✅ Successfully saved {len(data)} entries to {filename}")
        return True
        
    except Exception as e:
        print(f"❌ Error saving data: {e}")
        return False

def main():
    """Main conversion function"""
    print("🔄 Converting bingo arrays to new format...")
    print("="*50)
    
    # Load existing data
    data = load_existing_data()
    if not data:
        return
    
    # Convert to new format
    print("\n🔄 Converting data format...")
    new_data = convert_to_new_format(data)
    
    if not new_data:
        print("❌ No valid data to convert")
        return
    
    # Show sample of converted data
    print(f"\n📋 Sample of converted data (first entry):")
    if new_data:
        sample = new_data[0]
        print(f"   ID: {sample['id']}")
        print(f"   Cartela No: {sample['cartela_no']}")
        print(f"   Bingo Numbers: {sample['bingo_numbers'][:10]}... (showing first 10)")
    
    # Save to new file
    print(f"\n💾 Saving to arrays.json...")
    if save_new_format(new_data):
        print("\n🎉 Conversion completed successfully!")
        print(f"📊 Total entries converted: {len(new_data)}")
        print(f"📁 Output file: arrays.json")
    else:
        print("\n❌ Conversion failed")

if __name__ == "__main__":
    main()
