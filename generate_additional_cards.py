#!/usr/bin/env python3
"""
Generate 15,000 additional unique bingo cards
Uses the same Random class logic as BingoBoard.jsx with seed 75
Ensures no duplicate number combinations
"""

import json
import os
from typing import List, Dict, Any, Set, Tuple
import time

class Random:
    """Seedable random number generator - same as BingoBoard.jsx"""
    def __init__(self, seed: int):
        self.seed = seed
    
    def next_int(self, max_val: int) -> int:
        """Generate next random integer - same algorithm as JavaScript version"""
        self.seed = (self.seed * 9301 + 49297) % 233280
        return int((self.seed / 233280) * max_val)

def generate_valid_bingo_card(random_gen: Random) -> List[int]:
    """
    Generate a valid bingo card with proper column constraints
    B: 1-15, I: 16-30, N: 31-45, G: 46-60, O: 61-75
    Returns 24 numbers (excluding center FREE space)
    """
    # Define column ranges
    column_ranges = [
        (1, 15),   # B column
        (16, 30),  # I column  
        (31, 45),  # <PERSON> column
        (46, 60),  # G column
        (61, 75)   # O column
    ]
    
    bingo_numbers = []
    
    # Generate 5 rows x 5 columns, skip center (2,2)
    for row in range(5):
        for col in range(5):
            # Skip center cell (FREE space)
            if row == 2 and col == 2:
                continue
                
            # Get valid range for this column
            min_val, max_val = column_ranges[col]
            
            # Generate unique number for this column
            attempts = 0
            while attempts < 100:  # Prevent infinite loops
                number = random_gen.next_int(max_val - min_val + 1) + min_val
                
                # Check if number is already used in this card
                if number not in bingo_numbers:
                    bingo_numbers.append(number)
                    break
                    
                attempts += 1
            
            if attempts >= 100:
                # If we can't find a unique number, regenerate the whole card
                return generate_valid_bingo_card(random_gen)
    
    return bingo_numbers

def card_to_signature(numbers: List[int]) -> str:
    """Convert card numbers to a signature for duplicate detection"""
    return ','.join(map(str, sorted(numbers)))

def load_existing_cards(filename: str = "arrays.json") -> Tuple[List[Dict[str, Any]], Set[str]]:
    """Load existing cards and create signature set for duplicate detection"""
    try:
        if not os.path.exists(filename):
            print(f"❌ File not found: {filename}")
            return [], set()
            
        with open(filename, 'r') as f:
            existing_data = json.load(f)
        
        # Create signatures of existing cards
        existing_signatures = set()
        for card in existing_data:
            signature = card_to_signature(card['bingo_numbers'])
            existing_signatures.add(signature)
        
        print(f"✅ Loaded {len(existing_data)} existing cards")
        print(f"📊 Created {len(existing_signatures)} unique signatures")
        
        return existing_data, existing_signatures
        
    except Exception as e:
        print(f"❌ Error loading existing cards: {e}")
        return [], set()

def generate_additional_cards(count: int = 15000, start_id: int = 12001) -> List[Dict[str, Any]]:
    """Generate additional unique bingo cards"""
    print(f"🎯 Generating {count} additional unique bingo cards...")
    
    # Load existing cards to avoid duplicates
    existing_cards, existing_signatures = load_existing_cards()
    
    # Initialize random generator with seed 75 (same as BingoBoard.jsx)
    # But advance it past the existing cards to get new sequences
    random_gen = Random(75)
    
    # Advance the random generator past existing cards
    # This ensures we get different numbers than the existing ones
    for _ in range(len(existing_cards) * 25):  # 25 calls per card (including center)
        random_gen.next_int(75)
    
    new_cards = []
    generated_signatures = set()
    attempts = 0
    max_attempts = count * 10  # Allow some retries for uniqueness
    
    print("🔄 Generating cards...")
    start_time = time.time()
    
    while len(new_cards) < count and attempts < max_attempts:
        attempts += 1
        
        # Generate a new card
        bingo_numbers = generate_valid_bingo_card(random_gen)
        signature = card_to_signature(bingo_numbers)
        
        # Check for duplicates
        if signature not in existing_signatures and signature not in generated_signatures:
            card_data = {
                "id": start_id + len(new_cards),
                "cartela_no": len(existing_cards) + len(new_cards) + 1,
                "bingo_numbers": bingo_numbers
            }
            
            new_cards.append(card_data)
            generated_signatures.add(signature)
            
            # Progress update
            if len(new_cards) % 1000 == 0:
                elapsed = time.time() - start_time
                rate = len(new_cards) / elapsed if elapsed > 0 else 0
                print(f"   Generated {len(new_cards)}/{count} cards ({rate:.1f} cards/sec)")
    
    if len(new_cards) < count:
        print(f"⚠️  Warning: Only generated {len(new_cards)}/{count} unique cards after {attempts} attempts")
    else:
        elapsed = time.time() - start_time
        print(f"✅ Successfully generated {len(new_cards)} unique cards in {elapsed:.1f} seconds")
    
    return new_cards

def save_combined_cards(existing_cards: List[Dict[str, Any]], new_cards: List[Dict[str, Any]], 
                       filename: str = "arrays.json") -> bool:
    """Save combined existing and new cards to file"""
    try:
        combined_cards = existing_cards + new_cards
        
        with open(filename, 'w') as f:
            json.dump(combined_cards, f, indent=2)
        
        print(f"✅ Successfully saved {len(combined_cards)} total cards to {filename}")
        return True
        
    except Exception as e:
        print(f"❌ Error saving combined cards: {e}")
        return False

def validate_card_uniqueness(cards: List[Dict[str, Any]]) -> bool:
    """Validate that all cards are unique"""
    signatures = set()
    duplicates = 0
    
    for i, card in enumerate(cards):
        signature = card_to_signature(card['bingo_numbers'])
        if signature in signatures:
            duplicates += 1
            print(f"⚠️  Duplicate found at index {i}: ID {card['id']}")
        else:
            signatures.add(signature)
    
    if duplicates == 0:
        print(f"✅ All {len(cards)} cards are unique")
        return True
    else:
        print(f"❌ Found {duplicates} duplicate cards")
        return False

def main():
    """Main function"""
    print("🎯 BINGO CARD GENERATOR")
    print("=" * 50)
    print("Adding 15,000 unique bingo cards using seed 75")
    print("=" * 50)
    
    # Load existing cards
    existing_cards, _ = load_existing_cards()
    
    if not existing_cards:
        print("❌ No existing cards found. Please ensure arrays.json exists.")
        return
    
    # Generate new cards
    new_cards = generate_additional_cards(15000, 12001)
    
    if not new_cards:
        print("❌ Failed to generate new cards")
        return
    
    # Validate uniqueness
    print(f"\n🔍 Validating uniqueness of all {len(existing_cards) + len(new_cards)} cards...")
    all_cards = existing_cards + new_cards
    
    if not validate_card_uniqueness(all_cards):
        print("❌ Uniqueness validation failed")
        return
    
    # Save combined data
    print(f"\n💾 Saving combined data...")
    if save_combined_cards(existing_cards, new_cards):
        print("\n🎉 Successfully added 15,000 unique bingo cards!")
        print(f"📊 Total cards: {len(all_cards)}")
        print(f"📁 File: arrays.json")
        
        # Show sample of new cards
        print(f"\n📋 Sample of new cards:")
        for i in range(min(3, len(new_cards))):
            card = new_cards[i]
            print(f"   ID {card['id']}: {card['bingo_numbers'][:8]}... (showing first 8)")
    else:
        print("❌ Failed to save combined data")

if __name__ == "__main__":
    main()
