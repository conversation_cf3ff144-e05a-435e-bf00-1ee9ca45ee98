#!/usr/bin/env python3
"""
Generate remaining unique bingo cards using improved algorithm
Uses multiple seeds and better uniqueness strategies
"""

import json
import os
import random
from typing import List, Dict, Any, Set, Tuple
import time
import itertools

class ImprovedRandom:
    """Improved random number generator with multiple seeds"""
    def __init__(self, base_seed: int = 75):
        self.base_seed = base_seed
        self.current_seed = base_seed
        self.call_count = 0
    
    def next_int(self, max_val: int) -> int:
        """Generate next random integer with improved distribution"""
        self.call_count += 1
        # Use a combination of the original algorithm and call count for better distribution
        self.current_seed = (self.current_seed * 9301 + 49297 + self.call_count) % 233280
        return int((self.current_seed / 233280) * max_val)
    
    def reseed(self, new_seed: int):
        """Reseed the generator"""
        self.current_seed = new_seed
        self.call_count = 0

def generate_bingo_card_improved(random_gen: ImprovedRandom, attempt_num: int = 0) -> List[int]:
    """
    Generate a valid bingo card with improved uniqueness
    Uses different strategies based on attempt number
    """
    # Column ranges for bingo
    column_ranges = [
        (1, 15),   # B column
        (16, 30),  # I column  
        (31, 45),  # N column
        (46, 60),  # G column
        (61, 75)   # O column
    ]
    
    bingo_numbers = []
    
    # For later attempts, use more randomization
    if attempt_num > 1000:
        # Reseed with a different value to get different sequences
        random_gen.reseed(random_gen.base_seed + attempt_num)
    
    # Generate numbers for each position (skip center)
    positions = [(row, col) for row in range(5) for col in range(5) if not (row == 2 and col == 2)]
    
    for row, col in positions:
        min_val, max_val = column_ranges[col]
        
        # Try to find a unique number for this position
        attempts = 0
        while attempts < 50:
            number = random_gen.next_int(max_val - min_val + 1) + min_val
            
            if number not in bingo_numbers:
                bingo_numbers.append(number)
                break
                
            attempts += 1
        
        if attempts >= 50:
            # If we can't find a unique number, start over with different seed
            new_seed = random_gen.base_seed + attempt_num + len(bingo_numbers)
            random_gen.reseed(new_seed)
            return generate_bingo_card_improved(random_gen, attempt_num + 1)
    
    return bingo_numbers

def generate_systematic_cards(count: int, existing_signatures: Set[str], start_id: int) -> List[Dict[str, Any]]:
    """Generate cards using systematic approach for better uniqueness"""
    print(f"🔄 Using systematic generation for {count} cards...")
    
    new_cards = []
    generated_signatures = set()
    
    # Use multiple random generators with different seeds
    generators = [ImprovedRandom(75 + i * 1000) for i in range(10)]
    current_gen = 0
    
    attempts = 0
    max_attempts = count * 20
    
    start_time = time.time()
    
    while len(new_cards) < count and attempts < max_attempts:
        attempts += 1
        
        # Rotate between generators for better distribution
        random_gen = generators[current_gen % len(generators)]
        current_gen += 1
        
        # Generate card
        bingo_numbers = generate_bingo_card_improved(random_gen, attempts)
        signature = ','.join(map(str, sorted(bingo_numbers)))
        
        # Check uniqueness
        if signature not in existing_signatures and signature not in generated_signatures:
            card_data = {
                "id": start_id + len(new_cards),
                "cartela_no": 5000 + len(new_cards) + 1,  # Continue from existing cartela numbers
                "bingo_numbers": bingo_numbers
            }
            
            new_cards.append(card_data)
            generated_signatures.add(signature)
            
            # Progress update
            if len(new_cards) % 500 == 0:
                elapsed = time.time() - start_time
                rate = len(new_cards) / elapsed if elapsed > 0 else 0
                print(f"   Generated {len(new_cards)}/{count} cards ({rate:.1f} cards/sec)")
    
    elapsed = time.time() - start_time
    print(f"✅ Generated {len(new_cards)} cards in {elapsed:.1f} seconds")
    
    return new_cards

def load_current_data() -> Tuple[List[Dict[str, Any]], Set[str]]:
    """Load current arrays.json data"""
    try:
        with open("arrays.json", 'r') as f:
            data = json.load(f)
        
        signatures = set()
        for card in data:
            signature = ','.join(map(str, sorted(card['bingo_numbers'])))
            signatures.add(signature)
        
        print(f"✅ Loaded {len(data)} existing cards")
        return data, signatures
        
    except Exception as e:
        print(f"❌ Error loading data: {e}")
        return [], set()

def main():
    """Generate remaining cards to reach 20,000 total"""
    print("🎯 GENERATING REMAINING BINGO CARDS")
    print("=" * 50)
    
    # Load current data
    current_data, existing_signatures = load_current_data()
    
    if not current_data:
        print("❌ No existing data found")
        return
    
    current_count = len(current_data)
    target_count = 20000
    needed_count = target_count - current_count
    
    print(f"📊 Current cards: {current_count}")
    print(f"🎯 Target cards: {target_count}")
    print(f"🔄 Need to generate: {needed_count}")
    
    if needed_count <= 0:
        print("✅ Already have enough cards!")
        return
    
    # Find the next ID to use
    max_id = max(card['id'] for card in current_data)
    next_id = max_id + 1
    
    print(f"🆔 Starting ID: {next_id}")
    
    # Generate remaining cards
    new_cards = generate_systematic_cards(needed_count, existing_signatures, next_id)
    
    if len(new_cards) < needed_count:
        print(f"⚠️  Only generated {len(new_cards)}/{needed_count} additional cards")
    
    # Combine and save
    combined_data = current_data + new_cards
    
    print(f"\n💾 Saving {len(combined_data)} total cards...")
    
    try:
        with open("arrays.json", 'w') as f:
            json.dump(combined_data, f, indent=2)
        
        print(f"✅ Successfully saved {len(combined_data)} cards to arrays.json")
        
        # Validate uniqueness
        signatures = set()
        duplicates = 0
        for card in combined_data:
            sig = ','.join(map(str, sorted(card['bingo_numbers'])))
            if sig in signatures:
                duplicates += 1
            else:
                signatures.add(sig)
        
        if duplicates == 0:
            print(f"✅ All {len(combined_data)} cards are unique")
        else:
            print(f"⚠️  Found {duplicates} duplicates")
        
        print(f"\n🎉 Final count: {len(combined_data)} unique bingo cards")
        
    except Exception as e:
        print(f"❌ Error saving data: {e}")

if __name__ == "__main__":
    main()
