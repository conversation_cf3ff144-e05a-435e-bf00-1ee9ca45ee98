#!/usr/bin/env python3
"""
Add F to the middle of bingo_numbers arrays in data.json
12 numbers + F + 12 numbers, without changing anything else
"""

import json

def add_f_to_data_json():
    """Add F to the middle of each bingo_numbers array in data.json"""
    print("🔄 Adding F to the middle of each bingo card in data.json...")
    
    try:
        # Load data.json
        with open("data.json", 'r') as f:
            data = json.load(f)
        
        print(f"✅ Loaded {len(data)} cards from data.json")
        
        # Process each card
        modified_count = 0
        for card in data:
            bingo_numbers = card['bingo_numbers']
            
            # Verify we have exactly 24 numbers
            if len(bingo_numbers) == 24:
                # Split into two halves and insert F in the middle
                first_half = bingo_numbers[:12]  # First 12 numbers
                second_half = bingo_numbers[12:]  # Last 12 numbers
                
                # Create new array: 12 numbers + F + 12 numbers
                card['bingo_numbers'] = first_half + ['F'] + second_half
                modified_count += 1
            else:
                print(f"⚠️  Warning: Card ID {card['id']} has {len(bingo_numbers)} numbers instead of 24")
        
        print(f"✅ Modified {modified_count} cards")
        
        # Save with F without quotes
        with open("data.json", 'w') as f:
            json_str = json.dumps(data, indent=2)
            # Replace "F" with F (without quotes)
            json_str = json_str.replace('"F"', 'F')
            f.write(json_str)
        
        print(f"✅ Successfully saved modified data.json")
        print(f"📊 Each card now has 25 items: 12 numbers + F + 12 numbers")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def verify_data_json():
    """Verify the modifications in data.json"""
    print("\n🔍 Verifying data.json modifications...")
    
    try:
        # Read as text to check F without quotes
        with open("data.json", 'r') as f:
            content = f.read()
        
        # Count occurrences of F (should be equal to number of cards)
        f_count = content.count('F,') + content.count('F\n')  # F followed by comma or newline
        
        # Also load as JSON to verify structure
        data = json.loads(content.replace('F', '"F"'))  # Temporarily add quotes for JSON parsing
        
        print(f"📊 Verification Results:")
        print(f"   Total cards: {len(data)}")
        print(f"   F occurrences found: {f_count}")
        print(f"   Sample card length: {len(data[0]['bingo_numbers'])}")
        
        # Check first card format
        sample = data[0]
        print(f"\n📋 Sample card (ID {sample['id']}):")
        print(f"   First 12: {sample['bingo_numbers'][:12]}")
        print(f"   Center (pos 12): {sample['bingo_numbers'][12]}")
        print(f"   Last 12: {sample['bingo_numbers'][13:]}")
        
        if f_count == len(data):
            print("✅ All cards have F in the correct position!")
            return True
        else:
            print("❌ F count doesn't match card count")
            return False
            
    except Exception as e:
        print(f"❌ Error during verification: {e}")
        return False

def main():
    """Main function"""
    print("🎯 ADDING F TO DATA.JSON")
    print("=" * 50)
    
    if add_f_to_data_json():
        verify_data_json()
    else:
        print("❌ Failed to add F to data.json")

if __name__ == "__main__":
    main()
