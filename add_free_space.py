#!/usr/bin/env python3
"""
Add letter 'F' in the middle of each bingo_numbers array
12 numbers + 'F' + 12 numbers = 25 items total
"""

import json

def add_free_space_to_arrays():
    """Add 'F' in the middle of each bingo_numbers array"""
    print("🔄 Adding 'F' (FREE space) to the middle of each bingo card...")
    
    try:
        # Load current data
        with open("arrays.json", 'r') as f:
            data = json.load(f)
        
        print(f"✅ Loaded {len(data)} cards")
        
        # Process each card
        modified_count = 0
        for card in data:
            bingo_numbers = card['bingo_numbers']
            
            # Verify we have exactly 24 numbers
            if len(bingo_numbers) == 24:
                # Split into two halves and insert 'F' in the middle
                first_half = bingo_numbers[:12]  # First 12 numbers
                second_half = bingo_numbers[12:]  # Last 12 numbers
                
                # Create new array: 12 numbers + 'F' + 12 numbers
                card['bingo_numbers'] = first_half + ['F'] + second_half
                modified_count += 1
            else:
                print(f"⚠️  Warning: Card ID {card['id']} has {len(bingo_numbers)} numbers instead of 24")
        
        print(f"✅ Modified {modified_count} cards")
        
        # Verify the changes
        sample_card = data[0]
        print(f"\n📋 Sample card (ID {sample_card['id']}):")
        print(f"   Length: {len(sample_card['bingo_numbers'])}")
        print(f"   First 12: {sample_card['bingo_numbers'][:12]}")
        print(f"   Center: {sample_card['bingo_numbers'][12]}")
        print(f"   Last 12: {sample_card['bingo_numbers'][13:]}")
        
        # Save the modified data
        with open("arrays.json", 'w') as f:
            json.dump(data, f, indent=2)
        
        print(f"\n✅ Successfully saved modified data to arrays.json")
        print(f"📊 Each card now has 25 items: 12 numbers + 'F' + 12 numbers")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def verify_modification():
    """Verify that all cards have the correct format"""
    print("\n🔍 Verifying modifications...")
    
    try:
        with open("arrays.json", 'r') as f:
            data = json.load(f)
        
        correct_format = 0
        incorrect_format = 0
        
        for card in data:
            bingo_numbers = card['bingo_numbers']
            
            # Check if format is correct: 25 items with 'F' at position 12 (index 12)
            if (len(bingo_numbers) == 25 and 
                bingo_numbers[12] == 'F' and
                all(isinstance(x, int) for x in bingo_numbers[:12]) and
                all(isinstance(x, int) for x in bingo_numbers[13:])):
                correct_format += 1
            else:
                incorrect_format += 1
                print(f"⚠️  Card ID {card['id']} has incorrect format")
        
        print(f"📊 Verification Results:")
        print(f"   Correct format: {correct_format}")
        print(f"   Incorrect format: {incorrect_format}")
        
        if incorrect_format == 0:
            print("🎉 All cards have the correct format!")
            return True
        else:
            print("❌ Some cards have incorrect format")
            return False
            
    except Exception as e:
        print(f"❌ Error during verification: {e}")
        return False

def main():
    """Main function"""
    print("🎯 ADDING FREE SPACE TO BINGO CARDS")
    print("=" * 50)
    
    if add_free_space_to_arrays():
        verify_modification()
    else:
        print("❌ Failed to add FREE space")

if __name__ == "__main__":
    main()
